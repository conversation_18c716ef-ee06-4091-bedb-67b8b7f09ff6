import Branding from "@/components/blocks/branding";
import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import Feature3 from "@/components/blocks/feature3";
import Hero from "@/components/blocks/hero";
import HeroWithCompare from "@/components/blocks/hero-with-compare";
import ImageCompareGallery from "@/components/blocks/image-compare-gallery";
import Pricing from "@/components/blocks/pricing";
import Showcase from "@/components/blocks/showcase";
import Stats from "@/components/blocks/stats";
import Testimonial from "@/components/blocks/testimonial";
import { getLandingPage } from "@/services/page";
import { Generator4o } from "@/components/generator4o";
import { Kontext } from "@/components/kontext";


export const runtime = "edge";

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}) {
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`;
  }

  return {
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function LandingPage({
  params: { locale },
}: {
  params: { locale: string };
}) {
  const page = await getLandingPage(locale);

  // These can be updated with the URLs provided by the user
  const compareImages = {
    originalSrc: "https://pic.restore-old-photos.com/old-damaged-photo-of-woman-before-restoration.jpg",
    modifiedSrc: "https://pic.restore-old-photos.com/old-photo-of-woman-restored.png"
  };

  // 示例图片对比数据 - 这些应该替换为实际的图片URL
  const compareGroups = [
    {
      id: 1,
      originalSrc: "https://pic.restore-old-photos.com/grandma-photo-damaged-before.jpg",
      modifiedSrc: "https://pic.restore-old-photos.com/grandma-photo-restored-after.png",
      alt: "Restore Grandma Old Photos"
    },
    {
      id: 2,
      originalSrc: "https://pic.restore-old-photos.com/mother-daughter-damaged-photo-before-restoration.jpg",
      modifiedSrc: "https://pic.restore-old-photos.com/mother-daughter-photo-restored-after-repair.png",
      alt: "Restore Mother Daughter Damaged Old Photos"
    },
    {
      id: 3,
      originalSrc: "https://pic.restore-old-photos.com/sister-damaged-photo-before-restoration.jpg",
      modifiedSrc: "https://pic.restore-old-photos.com/sister-photo-ai-restored.png",
      alt: "Restore Sister Damaged Old Photos"
    },
    {
      id: 4,
      originalSrc: "https://pic.restore-old-photos.com/woman-damaged-photo-before-restoration.jpg",
      modifiedSrc: "https://pic.restore-old-photos.com/woman-photo-ai-restored.jpg",
      alt: "Restore Woman Damaged Old Photos"
    },
    {
      id: 5,
      originalSrc: "https://pic.restore-old-photos.com/family-damaged-photo-before-restoration.jpg",
      modifiedSrc: "https://pic.restore-old-photos.com/family-photo-ai-restored.jpg",
      alt: "Restore Family Damaged Old Photos"
    },
    {
      id: 6,
      originalSrc: "https://pic.restore-old-photos.com/lunch-atop-a-skyscraper-original-black-and-white.jpg",
      modifiedSrc: "https://pic.restore-old-photos.com/restore-old-photos-lunch-atop-a-skyscraper-colorized.jpg",
      alt: "Restore Lunch atop a Skyscraper Damaged Old Photos"
    }
  ];

  return (
    <>
      {page.hero && <HeroWithCompare 
        hero={page.hero} 
        compareImages={compareImages}
      />}
      <Kontext />
      
      
      {/* 图片对比画廊 */}
      <ImageCompareGallery 
        title="Restore Old Photos - Bring Memories Back to Life"
        description="See how we restore old photos with advanced AI. From cracks and fades to full-color revival, these transformations show how your treasured memories can be preserved and enhanced."
        compareGroups={compareGroups} 
      />

      {page.branding && <Branding section={page.branding} />}
      {page.introduce && <Feature1 section={page.introduce} />}
      {page.benefit && <Feature2 section={page.benefit} />}
      {page.usage && <Feature3 section={page.usage} />}
      
      
      
      {page.feature && <Feature section={page.feature} />}
      {page.showcase && <Showcase section={page.showcase} />}
      {page.stats && <Stats section={page.stats} />}
      {page.pricing && <Pricing pricing={page.pricing} />}
      {page.testimonial && <Testimonial section={page.testimonial} />}
      {page.faq && <FAQ section={page.faq} />}
      {page.cta && <CTA section={page.cta} />}
      
    </>
  );
}
