import { NextResponse } from "next/server";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { getSupabaseClient } from "@/models/db";
import { auth } from "@/auth";
import { getUserUuid } from "@/services/user";

export const runtime = 'edge';

const s3Client = new S3Client({
  region: "auto",
  endpoint: process.env.STORAGE_ENDPOINT,
  credentials: {
    accessKeyId: process.env.STORAGE_ACCESS_KEY || "",
    secretAccessKey: process.env.STORAGE_SECRET_KEY || "",
  },
  forcePathStyle: true
});

export async function POST(req: Request) {
  try {
    // 验证用户身份
    const session = await auth();
    if (!session?.user) {
      console.log('Upload attempt without authentication');
      return NextResponse.json({ error: "Authentication required" }, { status: 401 });
    }

    // 获取用户 UUID
    const userUuid = await getUserUuid();
    if (!userUuid) {
      console.log('Upload attempt without valid user UUID');
      return NextResponse.json({ error: "Unable to verify user identity" }, { status: 401 });
    }

    const { sourceUrl, filename, taskId, _sessionCheck } = await req.json();

    if (!sourceUrl || !filename) {
      return NextResponse.json({ error: "Source URL and filename are required" }, { status: 400 });
    }

    // 记录上传请求信息
    console.log(`Image upload request: filename=${filename}, user=${userUuid}, taskId=${taskId || 'none'}`);

    // 下载源图片
    const response = await fetch(sourceUrl);
    const imageBuffer = await response.arrayBuffer();

    // 上传到R2
    const uploadCommand = new PutObjectCommand({
      Bucket: process.env.STORAGE_BUCKET || "",
      Key: filename,
      Body: Buffer.from(imageBuffer),
      ContentType: 'image/png',
    });

    await s3Client.send(uploadCommand);
    
    // 构建新的URL
    const newUrl = `https://img.restore-old-photos.com/${filename}`;

    // 如果提供了taskId，更新数据库
    if (taskId) {
      const supabase = getSupabaseClient();
      await supabase
        .from("4o_generations")
        .update({
          generated_image_url: newUrl
        })
        .eq("task_id", taskId);
    }

    return NextResponse.json({ 
      success: true,
      url: newUrl
    });

  } catch (error) {
    console.error("Error uploading image:", error);
    return NextResponse.json(
      { error: "Failed to upload image" },
      { status: 500 }
    );
  }
} 