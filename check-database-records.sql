-- Check pending records in 4o_generations table
SELECT 
    id,
    task_id,
    callback_id,
    status,
    created_at,
    completed_at,
    generated_image_url,
    original_image_url,
    prompt
FROM "4o_generations" 
WHERE status = 'pending' 
ORDER BY created_at DESC 
LIMIT 10;

-- Check if there are any records with the specific callback_id from your example
SELECT 
    id,
    task_id,
    callback_id,
    status,
    created_at,
    completed_at,
    generated_image_url
FROM "4o_generations" 
WHERE callback_id = 'a83ede21-e3a1-4b94-97df-2d9378a418d2';

-- Check recent records to see the pattern
SELECT 
    id,
    task_id,
    callback_id,
    status,
    created_at,
    LEFT(generated_image_url, 50) as image_url_preview
FROM "4o_generations" 
ORDER BY created_at DESC 
LIMIT 5;
