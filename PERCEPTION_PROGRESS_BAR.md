# 感知进度条设计文档

## 功能概述

新的感知进度条设计在真正开始图片生成之前，先显示一个40秒的准备阶段，让用户感觉系统在处理，提升用户体验。

## 设计特点

### 1. 无缝体验
- 用户看不到任何技术细节
- 进度条平滑前进，不会停止或后退
- 没有明显的阶段切换提示

### 2. 时间分配
- **准备阶段**: 40秒，进度从0%到60%
- **生成阶段**: 实际API处理时间，进度从60%到100%

### 3. 进度计算
```javascript
// 准备阶段：40秒内平滑到60%
const preparationDuration = 40000; // 40秒
const targetProgress = 60; // 准备阶段最高到60%
const updateInterval = 200; // 每200ms更新一次
const progressIncrement = targetProgress / (preparationDuration / updateInterval);
```

## 实现细节

### 状态管理
```typescript
const [status, setStatus] = useState<'IDLE' | 'UPLOADING' | 'PREPARING' | 'GENERATING' | 'COMPLETED' | 'ERROR'>('IDLE');
const [isPreparationPhase, setIsPreparationPhase] = useState(false);
```

### 阶段流程
1. **上传阶段** (`UPLOADING`): 图片上传到服务器
2. **准备阶段** (`PREPARING`): 40秒感知进度，0% → 60%
3. **生成阶段** (`GENERATING`): 真正的API调用和处理，60% → 100%
4. **完成阶段** (`COMPLETED`): 显示结果

### 进度条逻辑

#### 准备阶段进度
```javascript
useEffect(() => {
  if (isProcessing && status === 'PREPARING') {
    setIsPreparationPhase(true);
    
    const preparationDuration = 40000; // 40秒
    const targetProgress = 60;
    const updateInterval = 200;
    const totalUpdates = preparationDuration / updateInterval;
    const progressIncrement = targetProgress / totalUpdates;
    
    let currentUpdate = 0;
    
    const preparationInterval = setInterval(() => {
      currentUpdate++;
      const newProgress = Math.min(currentUpdate * progressIncrement, targetProgress);
      
      setPerceptionProgress(prev => Math.max(prev, newProgress));
      
      // 40秒后切换到生成阶段
      if (currentUpdate >= totalUpdates) {
        clearInterval(preparationInterval);
        setIsPreparationPhase(false);
        setStatus('GENERATING');
        // 开始真正的API调用
        if (result.originalImageUrl) {
          startActualGeneration(result.originalImageUrl);
        }
      }
    }, updateInterval);
    
    return () => clearInterval(preparationInterval);
  }
}, [isProcessing, status]);
```

#### 生成阶段进度
```javascript
useEffect(() => {
  if (isProcessing && status === 'GENERATING' && !isPreparationPhase) {
    // 从60%开始继续进度
    const startProgress = Math.max(perceptionProgress, 60);
    if (perceptionProgress < startProgress) {
      setPerceptionProgress(startProgress);
    }
    
    // 模拟进度增长
    const simulatedInterval = setInterval(() => {
      setPerceptionProgress(prev => {
        const maxProgress = Math.min(Math.max(progress + 5, 95), 99);
        
        let increment = 0;
        if (prev < 70) increment = 0.5;
        else if (prev < 80) increment = 0.3;
        else if (prev < 90) increment = 0.1;
        else increment = 0.05;
        
        const newProgress = Math.min(prev + increment, maxProgress);
        return Math.max(prev, newProgress);
      });
    }, 1000);
    
    return () => clearInterval(simulatedInterval);
  }
}, [isProcessing, status, progress, perceptionProgress, isPreparationPhase]);
```

### API调用时机
```javascript
const startActualGeneration = async (originalImageUrl: string) => {
  // 真正的API调用逻辑
  // 这个函数在准备阶段结束后被调用
};
```

### 轮询控制
```javascript
// 只在真正的生成阶段进行轮询
useEffect(() => {
  if (taskId && status === 'GENERATING' && !isPreparationPhase) {
    // 轮询任务状态
  }
}, [taskId, status, isProcessing, error, progress, isPreparationPhase]);
```

## 用户界面

### 进度条显示
```jsx
{/* 在准备阶段和生成阶段都显示进度条 */}
{(status === "PREPARING" || status === "GENERATING") && (
  <div className="w-full bg-blue-100 rounded-full h-2.5">
    <div 
      className="bg-blue-600 h-2.5 rounded-full transition-all duration-300" 
      style={{ width: `${perceptionProgress}%` }}
    ></div>
  </div>
)}
```

### 按钮文本
```jsx
{status === "PREPARING" ? (
  <>
    Preparing... <span className="numeric-content">{Math.round(perceptionProgress)}%</span>
  </>
) : status === "GENERATING" ? (
  <>
    Generating... <span className="numeric-content">{Math.round(perceptionProgress)}%</span>
  </>
) : "Processing..."}
```

## 优势

### 1. 用户体验
- 消除等待焦虑
- 提供持续的视觉反馈
- 感觉系统响应迅速

### 2. 技术优势
- 不影响实际处理逻辑
- 进度条永远向前
- 平滑的视觉过渡

### 3. 心理效应
- 用户感觉系统在"工作"
- 减少放弃率
- 提高满意度

## 注意事项

1. **进度一致性**: 确保进度条只增不减
2. **时间控制**: 40秒的准备时间是固定的
3. **状态同步**: 准备阶段和生成阶段的状态要正确切换
4. **错误处理**: 在任何阶段都要能正确处理错误
5. **资源清理**: 确保定时器被正确清理

## 测试建议

1. **功能测试**: 验证40秒准备阶段正常工作
2. **进度测试**: 确认进度条从0%平滑到100%
3. **切换测试**: 验证准备阶段到生成阶段的切换
4. **错误测试**: 在各个阶段测试错误处理
5. **性能测试**: 确认定时器不会造成内存泄漏
