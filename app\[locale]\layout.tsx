import "@/app/globals.css";

import { getMessages, getTranslations } from "next-intl/server";

import { AppContextProvider } from "@/contexts/app";
import { Inter as FontSans } from "next/font/google";
import { Metadata } from "next";
import { NextAuthSessionProvider } from "@/auth/session";
import { NextIntlClientProvider } from "next-intl";
import Script from "next/script";
import { ThemeProvider } from "@/providers/theme";
import { cn } from "@/lib/utils";

const fontSans = FontSans({
  subsets: ["latin", "latin-ext"],
  variable: "--font-sans",
  display: "swap",
  preload: true,
});

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}): Promise<Metadata> {
  const t = await getTranslations();

  return {
    title: t("metadata.title") || "Restore Old Photos",
    description: t("metadata.description") || "Restore old photos online with AI. Revive faded, damaged, or black-and-white pictures with photo restoration and colorization tools.",
    keywords: t("metadata.keywords") || "restore old photos, photo restoration, AI photo restorer, colorize old photos, fix old pictures, repair damaged photos, image restoration online",
    icons: {
      icon: [
        { url: '/favicon.ico' }
      ]
    }
  };
}

export default async function RootLayout({
  children,
  params: { locale },
}: Readonly<{
  children: React.ReactNode;
  params: { locale: string };
}>) {
  const messages = await getMessages();

  return (
    <html lang={locale} suppressHydrationWarning>
      <head>
        <meta charSet="utf-8" />
        <meta name="msvalidate.01" content="C9DAF78F80715937318CEAC64A60CA72" />
        <Script id="microsoft-clarity" strategy="afterInteractive">
          {`
            (function(c,l,a,r,i,t,y){
                c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
            })(window, document, "clarity", "script", "s07ixs4r1z");
          `}
        </Script>
        {/*
        <Script 
          id="sharethis" 
          src="https://platform-api.sharethis.com/js/sharethis.js#property=67f45113a47a1e001adc661c&product=sticky-share-buttons&source=platform" 
          strategy="afterInteractive"
        />
        */}
      </head>
      <body
        className={cn(
          "min-h-screen font-sans antialiased overflow-x-hidden photo-restore-bg",
          fontSans.variable
        )}
      >
        <div className="sharethis-sticky-share-buttons"></div>
        <NextIntlClientProvider messages={messages}>
          <NextAuthSessionProvider>
            <AppContextProvider>
              <ThemeProvider attribute="class" disableTransitionOnChange>
                {children}
              </ThemeProvider>
            </AppContextProvider>
          </NextAuthSessionProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
