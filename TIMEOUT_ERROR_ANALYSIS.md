# 连接超时错误分析和修复

## 错误信息
```
Task ID [448c919fa5ad492d875ce49725f8c64f] request error: [TypeError: fetch failed] {
  cause: [ConnectTimeoutError: Connect Timeout Error] {
    name: 'ConnectTimeoutError',
    code: 'UND_ERR_CONNECT_TIMEOUT',
    message: 'Connect Timeout Error'
  }
}
```

## 错误分析

### 错误类型
- **错误名称**: `ConnectTimeoutError`
- **错误代码**: `UND_ERR_CONNECT_TIMEOUT`
- **发生位置**: 在轮询任务状态时调用外部API

### 可能原因
1. **网络延迟**: 到AI服务器的网络连接延迟过高
2. **API服务器负载**: AI服务器响应缓慢或过载
3. **防火墙/代理**: 网络中间件阻塞或延迟连接
4. **DNS解析问题**: 域名解析缓慢
5. **缺少超时配置**: 默认超时时间过短

## 修复措施

### 1. 添加超时控制
为所有外部API调用添加了明确的超时控制：

#### 任务状态查询 (15秒超时)
```javascript
const controller = new AbortController();
const timeoutId = setTimeout(() => controller.abort(), 15000);

const response = await fetch(apiUrl, {
  method: "GET",
  headers: { /* ... */ },
  signal: controller.signal
});
clearTimeout(timeoutId);
```

#### 图片生成API (30秒超时)
```javascript
const controller = new AbortController();
const timeoutId = setTimeout(() => controller.abort(), 30000);

const response = await fetch(API_BASE_URL, {
  method: 'POST',
  headers: { /* ... */ },
  body: JSON.stringify(apiRequestBody),
  signal: controller.signal
});
```

#### 图片下载 (20秒超时)
```javascript
const downloadController = new AbortController();
const downloadTimeoutId = setTimeout(() => downloadController.abort(), 20000);

const imageResponse = await fetch(imageUrl, {
  signal: downloadController.signal
});
```

### 2. 改进错误处理
添加了针对不同超时错误的具体处理：

```javascript
catch (fetchError) {
  if (fetchError.name === 'AbortError') {
    console.error('Request timed out');
  } else if (fetchError.message.includes('ConnectTimeoutError') || 
             fetchError.message.includes('UND_ERR_CONNECT_TIMEOUT')) {
    console.error('Connection timeout error');
  } else if (fetchError.message.includes('fetch failed')) {
    console.error('Network fetch failed');
  }
}
```

### 3. 优雅降级
当API调用失败时，系统会：
1. 记录详细的错误信息
2. 检查数据库中的任务记录
3. 根据任务创建时间判断是否应该标记为完成
4. 向用户提供有意义的错误信息

## 用户体验改进

### 错误消息
- **超时错误**: "API request timed out. Please try again."
- **连接错误**: "Connection timeout. Please check your network and try again."
- **网络错误**: "Network error occurred. Please try again."

### 自动重试机制
- 轮询会尝试多种任务ID格式
- 如果API调用失败，会检查数据库状态
- 超过10分钟的任务会被标记为可能完成

## 监控建议

### 日志监控
关注以下日志模式：
- `ConnectTimeoutError`
- `UND_ERR_CONNECT_TIMEOUT`
- `AbortError`
- `fetch failed`

### 性能指标
- API响应时间
- 超时频率
- 成功率

### 告警设置
- 超时错误率 > 5%
- API响应时间 > 10秒
- 连续失败次数 > 3

## 预防措施

1. **监控网络质量**: 定期检查到AI服务器的网络连接
2. **负载均衡**: 考虑使用多个API端点
3. **缓存策略**: 对已完成的任务结果进行缓存
4. **重试机制**: 实现指数退避的重试策略
5. **健康检查**: 定期检查外部服务的可用性

## 下一步优化

1. **实现重试机制**: 对失败的请求进行自动重试
2. **添加断路器**: 当服务不可用时快速失败
3. **性能监控**: 添加APM工具监控API性能
4. **备用方案**: 准备备用的AI服务提供商
