"use client";

import { Check, Loader } from "lucide-react";
import { PricingItem, Pricing as PricingType } from "@/types/blocks/pricing";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useEffect, useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import Icon from "@/components/icon";
import { Label } from "@/components/ui/label";
import { loadStripe } from "@stripe/stripe-js";
import { toast } from "sonner";
import { useAppContext } from "@/contexts/app";

export default function Pricing({ pricing }: { pricing: PricingType }) {
  if (pricing.disabled) {
    return null;
  }

  const { user, setShowSignModal } = useAppContext();

  // 默认选择year计费周期
  const [billingCycle, setBillingCycle] = useState<'month' | 'year'>('year');
  const [isLoading, setIsLoading] = useState(false);
  const [productId, setProductId] = useState<string | null>(null);

  // 根据计费周期筛选价格项
  const filteredItems = pricing.items?.filter(item => {
    // 检查产品ID中是否包含monthly或yearly来判断周期
    if (billingCycle === 'month') {
      return item.product_id.includes('monthly');
    } else {
      return item.product_id.includes('yearly');
    }
  });

  const handleCheckout = async (item: PricingItem, cn_pay: boolean = false) => {
    try {
      if (!user) {
        setShowSignModal(true);
        return;
      }

      const params = {
        product_id: item.product_id,
        product_name: item.product_name,
        credits: item.credits,
        interval: item.interval,
        amount: cn_pay ? item.cn_amount : item.amount,
        currency: cn_pay ? "cny" : item.currency,
        valid_months: item.valid_months,
      };

      setIsLoading(true);
      setProductId(item.product_id);

      const response = await fetch("/api/checkout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(params),
      });

      if (response.status === 401) {
        setIsLoading(false);
        setProductId(null);

        setShowSignModal(true);
        return;
      }

      const { code, message, data } = await response.json();
      if (code !== 0) {
        toast.error(message);
        return;
      }

      const { public_key, session_id } = data;

      const stripe = await loadStripe(public_key);
      if (!stripe) {
        toast.error("checkout failed");
        return;
      }

      const result = await stripe.redirectToCheckout({
        sessionId: session_id,
      });

      if (result.error) {
        toast.error(result.error.message);
      }
    } catch (e) {
      console.log("checkout failed: ", e);

      toast.error("checkout failed");
    } finally {
      setIsLoading(false);
      setProductId(null);
    }
  };

  return (
    <section id={pricing.name} className="py-16 photo-restore-section">
      <div className="container max-w-6xl mx-auto px-4">
        <div className="mx-auto mb-12 text-center">
          <Badge variant="outline" className="mb-4 bg-white/50 backdrop-blur-sm">
            Pricing Plans
          </Badge>
          <h2 className="mb-4 text-4xl font-semibold lg:text-5xl text-gray-800">
            {pricing.title}
          </h2>
          <p className="text-muted-foreground lg:text-lg max-w-2xl mx-auto">
            {pricing.description}
          </p>
        </div>

        {/* 计费周期切换 */}
        <div className="flex justify-center mb-10">
          <div className="inline-flex items-center bg-white/70 backdrop-blur-sm rounded-full p-1.5 shadow-md border border-blue-100">
            <div className="relative flex">
              <button
                onClick={() => setBillingCycle('month')}
                className={`px-6 py-2.5 rounded-full text-sm font-medium transition-all duration-300 ${
                  billingCycle === 'month'
                    ? 'bg-primary text-white shadow-sm'
                    : 'text-gray-600 hover:text-primary hover:bg-blue-50/50'
                }`}
              >
                Monthly
              </button>
              <button
                onClick={() => setBillingCycle('year')}
                className={`px-6 py-2.5 rounded-full text-sm font-medium transition-all duration-300 ${
                  billingCycle === 'year'
                    ? 'bg-primary text-white shadow-sm'
                    : 'text-gray-600 hover:text-primary hover:bg-blue-50/50'
                }`}
              >
                Yearly
                <Badge
                  variant="secondary"
                  className="ml-2 bg-yellow-100 text-yellow-800 border-0"
                >
                  Save 30%
                </Badge>
              </button>
            </div>
          </div>
        </div>

        {/* 价格卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {filteredItems?.map((item, index) => {
            return (
              <div
                key={index}
                className={`relative rounded-2xl p-8 transition-all duration-300 hover:translate-y-[-5px] photo-restore-card ${
                  item.is_featured
                    ? "border-2 border-primary bg-white/90 backdrop-blur-sm shadow-xl"
                    : item.title === "Premium"  
                      ? "border-2 border-[#2F4538] bg-white/90 backdrop-blur-sm shadow-lg"
                      : "border border-blue-100 bg-white/80 backdrop-blur-sm shadow-md hover:shadow-lg"
                }`}
              >
                {/* Unlimited 标识 */}
                {item.title === "Premium" && (
                  <Badge
                    className="absolute -top-3 right-6 bg-[#2F4538] text-white border-0 font-medium px-3 py-1"
                  >
                    Premium
                  </Badge>
                )}
                {item.is_featured && (
                  <Badge
                    variant="secondary"
                    className="absolute -top-3 right-6 bg-blue-600 text-white border-0 px-3 py-1"
                  >
                    Popular
                  </Badge>
                )}

                <div className="flex flex-col h-full">
                  {/* 标题和描述 */}
                  <div className="mb-6">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">{item.title}</h3>
                    <p className="text-gray-600 text-sm">{item.description}</p>
                  </div>

                  {/* 价格显示 */}
                  <div className="mb-6">
                    <div className="flex items-baseline gap-2">
                      <span className="text-4xl font-bold text-primary">{item.price}</span>
                      {item.original_price && (
                        <span className="text-gray-500 line-through text-lg">
                          {item.original_price}
                        </span>
                      )}
                    </div>
                    {item.tip && (
                      <div className="text-green-600 text-sm mt-1 font-medium">
                        {item.tip}
                      </div>
                    )}
                    {billingCycle === 'year' && (
                      <div className="text-gray-500 text-sm mt-1">
                        One-time payment for 12 months
                      </div>
                    )}
                    {billingCycle === 'month' && (
                      <div className="text-gray-500 text-sm mt-1">
                        One-time payment for 1 month
                      </div>
                    )}
                  </div>

                  {/* 积分显示 */}
                  <div className="mb-6">
                    <div className="inline-block bg-blue-50/70 rounded-lg px-4 py-2 border border-blue-100">
                      {item.credits && (
                        <span className="text-lg font-semibold text-blue-700">
                          {item.credits} Credits
                          {item.valid_months && (
                            <span className="text-blue-500 text-sm ml-1">
                              (Valid for {item.valid_months} months)
                            </span>
                          )}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* 功能列表 */}
                  <div className="flex-grow">
                    {item.features && (
                      <>
                        {item.features_title && (
                          <div className="text-sm font-medium text-gray-500 mb-4">
                            {item.features_title}
                          </div>
                        )}
                        <ul className="space-y-4">
                          {item.features.map((feature, fi) => (
                            <li key={fi} className="flex items-start gap-3">
                              <div className="rounded-full bg-blue-100 p-1 flex items-center justify-center shrink-0">
                                <Check className="h-3 w-3 text-blue-600" />
                              </div>
                              <span className="text-gray-600 text-sm">{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </>
                    )}
                  </div>

                  {/* 支付按钮 */}
                  <div className="mt-8">
                    <Button
                      className={`w-full py-6 shadow-lg transition-all duration-300 hover:shadow-xl ${
                        item.is_featured
                          ? "bg-gradient-to-r from-blue-500 to-blue-700 hover:from-blue-600 hover:to-blue-800 text-white"
                          : item.title === "Premium"
                            ? "bg-[#2F4538] hover:bg-[#243a2d] text-white"
                            : "bg-white hover:bg-gray-50 text-gray-800 border border-gray-200"
                      }`}
                      onClick={() => handleCheckout(item)}
                      disabled={isLoading && productId === item.product_id}
                    >
                      {isLoading && productId === item.product_id ? (
                        <Loader className="mr-2 h-4 w-4 animate-spin" />
                      ) : null}
                      {item.button?.title || "One Time Pay"}
                      {item.button?.icon && (
                        <Icon name={item.button.icon} className="ml-2" />
                      )}
                    </Button>

                    {/* 支付宝/微信支付选项 */}
                    {item.cn_amount && item.cn_amount > 0 && (
                      <div className="mt-4 flex items-center justify-center gap-2">
                        <span className="text-sm text-gray-600">支付方式：</span>
                        <div
                          className="cursor-pointer hover:opacity-80 transition-opacity bg-white p-1 rounded shadow-sm"
                          onClick={() => {
                            if (!isLoading) {
                              handleCheckout(item, true);
                            }
                          }}
                        >
                          <img
                            src="/imgs/cnpay.png"
                            alt="cnpay"
                            className="h-8 object-contain rounded"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* 底部说明 */}
        <div className="text-center mt-10 text-sm text-gray-500 bg-white/70 backdrop-blur-sm py-4 px-6 rounded-lg max-w-2xl mx-auto shadow-sm border border-blue-50">
          All prices in USD. Monthly plan valid for 30 days, yearly plan valid for 365 days.
          <br />
          
        </div>
      </div>
    </section>
  );
}
