// Test script to simulate the Kontext callback
// This helps verify that the callback processing works correctly

// Based on your actual API response format
const testCallbackData = {
  "code": 200,
  "msg": "success",
  "data": {
    "taskId": "fluxkontext_061928afc3434599bcc93f02e96cf6cd",
    "paramJson": "{\"aspectRatio\":\"1:1\",\"callBackUrl\":\"https://restore-old-photos.com/api/callbacks/kontext-callback/a83ede21-e3a1-4b94-97df-2d9378a418d2\",\"enableTranslation\":true,\"inputImage\":\"https://img.restore-old-photos.com/damaged-photo-aaacbc2a-7650-4e2b-a725-398d6a641f43.png\",\"model\":\"flux-kontext-pro\",\"outputFormat\":\"jpeg\",\"prompt\":\"Restore and colorize this image. Remove any scratches or imperfections. make it vivid.\",\"promptUpsampling\":false}",
    "completeTime": "2025-08-05 20:08:05",
    "response": {
      "originImageUrl": null,
      "resultImageUrl": "https://tempfile.aiquickdraw.com/f/fluxkontext_061928afc3434599bcc93f02e96cf6cd_1754395683.jpg"
    },
    "successFlag": 1,
    "errorCode": null,
    "errorMessage": null,
    "createTime": "2025-08-05 20:07:58"
  }
};

async function testCallback() {
  const callbackId = "a83ede21-e3a1-4b94-97df-2d9378a418d2"; // Extract from the callBackUrl

  // Test with production URL since that's where the issue is
  const callbackUrl = `https://restore-old-photos.com/api/callbacks/kontext-callback/${callbackId}`;

  console.log("Testing callback with URL:", callbackUrl);
  console.log("Callback data:", JSON.stringify(testCallbackData, null, 2));

  try {
    const response = await fetch(callbackUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testCallbackData)
    });

    const result = await response.json();
    console.log("Response status:", response.status);
    console.log("Response data:", result);

    if (response.ok) {
      console.log("✅ Callback processed successfully!");
      console.log("Generated image should now be uploaded to R2 and database updated");
    } else {
      console.log("❌ Callback failed:", result.error);
    }
  } catch (error) {
    console.error("❌ Error testing callback:", error);
  }
}

// Also test with localhost if you're running locally
async function testLocalCallback() {
  const callbackId = "a83ede21-e3a1-4b94-97df-2d9378a418d2";
  const callbackUrl = `http://localhost:3000/api/callbacks/kontext-callback/${callbackId}`;

  console.log("\n--- Testing with localhost ---");
  console.log("Testing callback with URL:", callbackUrl);

  try {
    const response = await fetch(callbackUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testCallbackData)
    });

    const result = await response.json();
    console.log("Response status:", response.status);
    console.log("Response data:", result);

    if (response.ok) {
      console.log("✅ Local callback processed successfully!");
    } else {
      console.log("❌ Local callback failed:", result.error);
    }
  } catch (error) {
    console.error("❌ Error testing local callback:", error);
  }
}

// Run the tests
console.log("=== Testing Kontext Callback Processing ===");
testCallback().then(() => {
  // Uncomment the line below if you want to test locally too
  // testLocalCallback();
});
