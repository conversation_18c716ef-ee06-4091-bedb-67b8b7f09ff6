"use client";

import React from 'react';
import Image from 'next/image';
import { cn } from "@/lib/utils";

interface ImageCompareProps {
  originalSrc: string;
  modifiedSrc: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
}

const ImageCompare: React.FC<ImageCompareProps> = ({ 
  originalSrc, 
  modifiedSrc, 
  alt,
  width = 600,
  height = 600,
  className = ""
}) => {
  return (
    <div className={cn("w-full max-w-[1200px] mx-auto space-y-6 md:space-y-0 md:grid md:grid-cols-2 md:gap-8", className)}>
      {/* Original Image */}
      <div className="relative w-full aspect-square rounded-2xl overflow-hidden shadow-lg group">
        <Image 
          src={originalSrc} 
          alt={`Damaged old photo - ${alt}`} 
          fill 
          className="object-cover transition duration-300 ease-in-out group-hover:scale-105"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 800px"
          priority
          quality={95}
        />
        <div className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/70 to-transparent p-4">
          <span className="text-white text-sm md:text-base font-medium">Damaged Old Photo</span>
        </div>
      </div>

      {/* Modified Image */}
      <div className="relative w-full aspect-square rounded-2xl overflow-hidden shadow-lg group">
        <Image 
          src={modifiedSrc} 
          alt={`Restored photo - ${alt}`} 
          fill 
          className="object-cover transition duration-300 ease-in-out group-hover:scale-105"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 800px"
          priority
          quality={95}
        />
        <div className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/70 to-transparent p-4">
          <span className="text-white text-sm md:text-base font-medium">Restored Photo</span>
        </div>
      </div>
    </div>
  );
};

export default ImageCompare; 