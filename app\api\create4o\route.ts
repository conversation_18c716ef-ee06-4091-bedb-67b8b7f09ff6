import { NextResponse } from "next/server";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { v4 as uuidv4 } from 'uuid';
import { getUserUuid } from "@/services/user";
import { decreaseCredits, CreditsTransType, getUserCredits } from "@/services/credit";
import { getSupabaseClient } from "@/models/db";
import { findUserByUuid } from "@/models/user";

export const runtime = 'edge';

// 初始化 S3 客户端 (R2 兼容 S3 API)
const s3Client = new S3Client({
  region: "auto",
  endpoint: process.env.STORAGE_ENDPOINT,
  credentials: {
    accessKeyId: process.env.STORAGE_ACCESS_KEY || "",
    secretAccessKey: process.env.STORAGE_SECRET_KEY || "",
  },
  forcePathStyle: true
});

// Kie.ai API 配置
const KIE_API_URL = "https://kieai.erweima.ai/api/v1/gpt4o-image/generate";
const KIE_API_KEY = process.env.KIE_API_KEY || "";

export async function POST(req: Request) {
  try {
    const { image, prompt, size = "1:1", aspectRatio } = await req.json();

    if (!image) {
      return NextResponse.json(
        { error: "Image is required" },
        { status: 400 }
      );
    }

    if (!prompt) {
      return NextResponse.json(
        { error: "Prompt is required" },
        { status: 400 }
      );
    }

    // 获取用户 UUID
    const userUuid = await getUserUuid();
    
    // 1. 首先检查用户积分是否足够
    const requiredCredits = 3; // 需要的积分数量
    const userCredits = await getUserCredits(userUuid);
    
    if (userCredits.left_credits < requiredCredits) {
      return NextResponse.json(
        { 
          error: "insufficient_credits",
          message: `Insufficient credits. Required: ${requiredCredits}, Available: ${userCredits.left_credits}`
        },
        { status: 402 }
      );
    }

    // 处理base64图片数据
    if (image.startsWith('data:image/')) {
      try {
        // 从base64数据中提取实际的图片内容
        const base64Data = image.split(',')[1];
        const buffer = Buffer.from(base64Data, 'base64');
        
        // 生成唯一的文件名
        const fileExtension = image.split(';')[0].split('/')[1];
        const fileName = `${uuidv4()}.${fileExtension}`;
        
        // 上传到 R2 (用于存储)
        const command = new PutObjectCommand({
          Bucket: process.env.STORAGE_BUCKET || "",
          Key: fileName,
          Body: buffer,
          ContentType: `image/${fileExtension}`,
        });

        await s3Client.send(command);

        // 构建图片URL - 使用自定义域名
        const imageUrl = `${process.env.STORAGE_DOMAIN}/${fileName}`;
        console.log("Image uploaded to R2:", imageUrl);

        // 生成回调URL
        const callbackId = uuidv4();
        const callbackUrl = `${process.env.NEXT_PUBLIC_APP_URL}/api/callbacks/4o-callback/${callbackId}`;
        
        // 调用Kie.ai API进行图像生成
        const response = await fetch(KIE_API_URL, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": `Bearer ${KIE_API_KEY}`
          },
          body: JSON.stringify({
            fileUrl: imageUrl,
            prompt: prompt,
            size: size,
            callBackUrl: callbackUrl
          })
        });

        const responseData = await response.json();
        console.log("Kie.ai API response:", responseData);

        if (!response.ok || !responseData.data || !responseData.data.taskId) {
          console.error("Kie.ai API error:", responseData);
          throw new Error(responseData.message || "Failed to process image");
        }

        console.log("Kie.ai API response:", responseData);

        // 获取任务ID
        const taskId = responseData.data.taskId;
        
        // 不在这里扣除积分，改为在客户端确认图片生成成功后再扣除

        // 4. 保存生成记录
        let userInfo;
        try {
          userInfo = await findUserByUuid(userUuid);
          console.log("获取用户信息成功:", userInfo?.nickname);
        } catch (userError) {
          console.error("获取用户信息失败:", userError);
          userInfo = { nickname: "", email: "" };
        }
        
        try {
          console.log("准备连接数据库...");
          const supabase = getSupabaseClient();
          console.log("数据库连接成功, 准备插入数据");
          
          // 验证表是否存在
          const { data: tableExists, error: tableError } = await supabase
            .from("4o_generations")
            .select("count")
            .limit(1);
            
          if (tableError) {
            console.error("表验证失败:", tableError);
            throw new Error(`表验证失败: ${tableError.message}`);
          }
          
          console.log("表验证成功");
          
          const insertData = {
            user_uuid: userUuid,
            nickname: userInfo?.nickname || "",
            email: userInfo?.email || "",
            task_id: taskId,
            callback_id: callbackId,
            original_image_url: imageUrl,
            generated_image_url: null,
            prompt: "Create studio ghibli style photos",
            status: "pending",
            created_at: new Date().toISOString(),
            completed_at: null
          };
          
          console.log("插入数据:", JSON.stringify(insertData));
          
          const { data, error } = await supabase
            .from("4o_generations")
            .insert(insertData)
            .select();
            
          if (error) {
            console.error("数据库插入失败:", error);
            console.error("错误详情:", {
              code: error.code,
              message: error.message,
              details: error.details,
              hint: error.hint
            });
            throw new Error(`数据库插入失败: ${error.message}`);
          }
          
          console.log("数据库插入成功, 结果:", data);
        } catch (dbError) {
          console.error("数据库操作失败:", dbError);
          console.error("完整错误堆栈:", (dbError as Error).stack);
          // 不要在这里返回错误，让操作继续，因为积分已扣除
          // 记录错误但继续执行
        }

        return NextResponse.json({ 
          taskId: taskId,
          status: "pending",
          message: "Image generation task submitted successfully",
          requiredCredits: requiredCredits,
          creditsLeft: userCredits.left_credits 
        });

      } catch (error) {
        console.error("4o Image generation error:", error);
        // 如果出错，不扣除积分
        return NextResponse.json(
          { error: "Failed to process image" },
          { status: 500 }
        );
      }
    } else {
      return NextResponse.json(
        { error: "Invalid image format" },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Error processing 4o image request:", error);
    return NextResponse.json(
      { error: "Failed to process request" },
      { status: 500 }
    );
  }
} 