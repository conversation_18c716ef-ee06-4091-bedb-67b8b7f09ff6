import { NextResponse } from 'next/server';
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { v4 as uuidv4 } from 'uuid';
import { getUserUuid } from "@/services/user";
import { getUserCredits } from "@/services/credit";
import { getSupabaseClient } from "@/models/db";
import { findUserByUuid, getUserUuidsByEmail } from "@/models/user";
import { auth } from "@/auth";

export const runtime = 'edge';

export const maxDuration = 60; // Set maximum timeout to 60 seconds

// Initialize S3 client (R2 compatible with S3 API)
const s3Client = new S3Client({
  region: "auto",
  endpoint: process.env.STORAGE_ENDPOINT,
  credentials: {
    accessKeyId: process.env.STORAGE_ACCESS_KEY || "",
    secretAccessKey: process.env.STORAGE_SECRET_KEY || "",
  },
  forcePathStyle: true
});

// Kie.ai Flux Kontext API configuration
const API_BASE_URL = 'https://kieai.erweima.ai/api/v1/flux/kontext/generate';

export async function POST(request: Request) {
  try {
    // Ensure environment variables are correctly loaded
    if (!process.env.KIE_API_KEY) {
      //console.error('ERROR: environment variable is not set');
      return NextResponse.json(
        { error: "api_key_missing", message: "API key configuration error" },
        { status: 500 }
      );
    }
    
    const API_KEY = process.env.KIE_API_KEY;
    //console.log('Checking environment variables in POST request:');
    //console.log('- KIE_API_KEY exists:', !!process.env.KIE_API_KEY);
    //console.log('- API_KEY value (first 4 chars):', API_KEY ? `${API_KEY.substring(0, 4)}...` : 'not set');
   // console.log('- STORAGE_ENDPOINT exists:', !!process.env.STORAGE_ENDPOINT);
    //console.log('- NEXT_PUBLIC_APP_URL exists:', !!process.env.NEXT_PUBLIC_APP_URL);
    
    // Clone request for later use
    const requestClone = request.clone();
    
    // First parse the request body so we can use debug info
    let requestBody;
    try {
      requestBody = await requestClone.json();
      //console.log('Request body parsed, contains _debug info:', !!requestBody._debug);
      if (requestBody._debug) {
        console.log('Debug info:', JSON.stringify(requestBody._debug));
      }
    } catch (err) {
      console.error('Error parsing request body:', err);
      requestBody = {};
    }
    
    // Get session info, confirm user is logged in
    const session = await auth();
    console.log('Session info:', session ? 'Session exists' : 'Session does not exist');
    console.log('User ID:', session?.user?.id || 'not retrieved');
    console.log('User Email:', session?.user?.email || 'not retrieved');
    console.log('User UUID in session:', session?.user?.uuid || 'not present');

    // Don't immediately return 401 error, continue execution to record more info
    let authErrorMessage = null;
    if (!session?.user?.id && !session?.user?.uuid && !session?.user?.email) {
      console.log('⚠️ User not logged in, but continuing for debugging');
      authErrorMessage = 'User not logged in - trying alternative authentication';
    } else {
      console.log('✅ User logged in, ID:', session.user.id);
      console.log('✅ User details:', JSON.stringify({
        id: session.user.id,
        uuid: session.user.uuid,
        email: session.user.email
      }));
    }

    // Get user UUID
    let userUuid = "";
    try {
      // Get UUID from service
      userUuid = await getUserUuid();
      console.log('getUserUuid() returned user UUID:', userUuid || 'not retrieved');
      console.log('getUserUuid() implementation details:', 'Checking session.user.uuid and API keys');
      
      // If service didn't return UUID but session exists, try getting directly from session
      if (!userUuid && session?.user?.id) {
        userUuid = session.user.id; // Try using user.id as fallback
        console.log('Getting user ID directly from session as UUID:', userUuid);
      } 
      // If unable to get UUID through session ID, try getting through email
      else if (!userUuid && session?.user?.email) {
        //console.log('Trying to get UUID by email from database:', session.user.email);
        const uuids = await getUserUuidsByEmail(session.user.email);
        if (uuids && uuids.length > 0) {
          userUuid = uuids[0];
          //console.log('Got UUID by email from database:', userUuid);
        }
      }
      // If still can't get UUID but request body contains debug info, try using it
      else if (!userUuid && requestBody?._debug?.userId) {
        userUuid = requestBody._debug.userId;
        //console.log('Getting user ID from request body debug info as UUID:', userUuid);
      }
      // If unable to get UUID through userId, try getting through email in request body
      else if (!userUuid && requestBody?._debug?.userEmail) {
        //console.log('Trying to get UUID by email from request body:', requestBody._debug.userEmail);
        const uuids = await getUserUuidsByEmail(requestBody._debug.userEmail);
        if (uuids && uuids.length > 0) {
          userUuid = uuids[0];
          //console.log('Got UUID by email from request body:', userUuid);
        }
      }
      
      if (!userUuid) {
        console.log('⚠️ Unable to get user UUID');
        console.log('Debug session object:', JSON.stringify(session));
      } else {
        console.log('✅ Successfully got user UUID:', userUuid);
      }
    } catch (uuidError) {
      console.error('Error getting user UUID:', uuidError);
      // Continue execution, don't throw error yet
    }
    
    // Check if user UUID was retrieved
    if (!userUuid) {
      return NextResponse.json(
        { 
          error: "authentication_error", 
          message: "Unable to verify user identity, please log in again",
          debugInfo: { 
            sessionExists: !!session,
            userIdExists: !!session?.user?.id,
            userEmailExists: !!session?.user?.email,
            requestBodyHasDebug: !!requestBody?._debug,
            requestBodyHasEmail: !!requestBody?._debug?.userEmail
          }
        },
        { status: 401 }
      );
    }

    // Get parameters from request body
    const { prompt, imageUrl, aspectRatio = '1:1' } = requestBody;
    
    // Validate necessary parameters
    if (!prompt || !imageUrl) {
      return NextResponse.json(
        { message: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Check if user has enough credits
    const requiredCredits = 3; // Number of credits required
    const userCredits = await getUserCredits(userUuid);
    
    if (userCredits.left_credits < requiredCredits) {
      return NextResponse.json(
        { 
          error: "insufficient_credits",
          message: `Insufficient credits. Required: ${requiredCredits}, Available: ${userCredits.left_credits}`
        },
        { status: 402 }
      );
    }

    // Build parameters for Kontext API request
    const callbackId = uuidv4();
    const callbackUrl = `${process.env.NEXT_PUBLIC_APP_URL}/api/callbacks/kontext-callback/${callbackId}`;
    
    const apiRequestBody = {
      prompt,
      inputImage: imageUrl, // Input image URL
      aspectRatio,
      outputFormat: 'jpeg',
      promptUpsampling: false,
      model: 'flux-kontext-pro', // Optional: flux-kontext-max for more complex scenes
      callBackUrl: callbackUrl // Add callback URL
    };

    // Call Kontext API with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout for generation

    let response;
    try {
      response = await fetch(API_BASE_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${API_KEY}`
        },
        body: JSON.stringify(apiRequestBody),
        signal: controller.signal
      });
      clearTimeout(timeoutId);
    } catch (fetchError) {
      clearTimeout(timeoutId);
      console.error('Kontext API fetch error:', fetchError);

      if (fetchError instanceof Error) {
        if (fetchError.name === 'AbortError') {
          return NextResponse.json(
            { error: "api_timeout", message: "API request timed out. Please try again." },
            { status: 408 }
          );
        } else if (fetchError.message.includes('ConnectTimeoutError') ||
                   fetchError.message.includes('UND_ERR_CONNECT_TIMEOUT')) {
          return NextResponse.json(
            { error: "connection_timeout", message: "Connection timeout. Please check your network and try again." },
            { status: 408 }
          );
        }
      }

      return NextResponse.json(
        { error: "network_error", message: "Network error occurred. Please try again." },
        { status: 503 }
      );
    }

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Kontext API error:', errorData);
      return NextResponse.json(
        { message: `Generation failed: ${errorData.message || 'Unknown error'}` },
        { status: response.status }
      );
    }

    // Get Kontext API response
    const data = await response.json();
    console.log('response data:', JSON.stringify(data));

    // Extract task ID (may be in different locations or formats)
    let taskId = data.taskId;

    // If direct taskId field doesn't exist, try getting from other possible locations
    if (!taskId && data.data && data.data.taskId) {
      taskId = data.data.taskId;
      //console.log('Getting task ID from data.data.taskId:', taskId);
    } else if (!taskId && data.task_id) {
      taskId = data.task_id;
      //console.log('Getting task ID from data.task_id:', taskId);
    } else if (!taskId && data.id) {
      taskId = data.id;
      //console.log('Getting task ID from data.id:', taskId);
    }

    // Log complete API response for debugging
    console.log('Complete response:', JSON.stringify(data));
    console.log('Extracted task ID:', taskId);

    if (!taskId) {
      console.error('Unable to extract task ID from API response:', data);
      return NextResponse.json(
        { 
          message: 'Task ID not returned', 
          apiResponse: data,
          responseStatus: response.status
        },
        { status: 500 }
      );
    }

    // Get user information
    let userInfo;
    try {
      userInfo = await findUserByUuid(userUuid);
      console.log("Successfully retrieved user info:", userInfo?.nickname);
    } catch (userError) {
      console.error("Failed to get user info:", userError);
      userInfo = { nickname: "", email: "" };
    }
    
    // Save generation record to database
    try {
      //console.log("Preparing to connect to database...");
      const supabase = getSupabaseClient();
      //console.log("Database connection successful, preparing to insert data");
      
      // Create table if it doesn't exist
      try {
        const { data: tableCheck, error: tableError } = await supabase
          .from("4o_generations")
          .select("count")
          .limit(1);
          
        if (tableError && tableError.code === "42P01") { // Error code for table doesn't exist
          console.log("table doesn't exist, trying to create it...");
          // Table doesn't exist, try to create it
          // Note: In a production environment, should create tables via migration scripts or admin panel
          // This is just for demonstration
          // This code may not work in Edge runtime as it requires higher privileges
        }
      } catch (tableCheckError) {
        console.error("Error checking table:", tableCheckError);
        // Continue execution, let subsequent operations determine success
      }
      
      const insertData = {
        user_uuid: userUuid,
        nickname: userInfo?.nickname || "",
        email: userInfo?.email || "",
        task_id: taskId,
        callback_id: callbackId,
        original_image_url: imageUrl,
        generated_image_url: null,
        prompt: prompt,
        status: "pending",
        created_at: new Date().toISOString(),
        completed_at: null
      };
      
      console.log("Inserting data:", JSON.stringify(insertData));
      //console.log("Note: Initial status set to 'pending', will be updated to 'COMPLETED' when task finishes");
      
      const { data: insertResult, error } = await supabase
        .from("4o_generations")
        .insert(insertData)
        .select();
        
      if (error) {
        console.error("Database insertion failed:", error);
        console.error("Error details:", {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        });
        // Don't throw error, continue execution
      } else {
        console.log("Database insertion successful, result:", insertResult);
      }
    } catch (dbError) {
      console.error("Database operation failed:", dbError);
      // Don't return error here, let operation continue
    }

    // After saving generation record, return task ID directly without polling on server
    // Log usage to logs
    //console.log(`User ${userUuid} successfully used ervice, prompt: ${prompt}`);

    // Return task ID to frontend, frontend component will poll task status
    return NextResponse.json({
      taskId: taskId,
      status: "pending",
      message: "Image generation task successfully submitted",
      requiredCredits: requiredCredits,
      creditsLeft: userCredits.left_credits
    });
  } catch (error) {
    console.error('Error processing request:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Poll task status until completion
async function pollTaskStatus(taskId: string) {
  const maxAttempts = 30; // Maximum 30 polling attempts
  const pollInterval = 2000; // 2 second polling interval
  
  // Get API key from environment variable
  if (!process.env.KIE_API_KEY) {
    console.error('ERROR: environment variable is not set for polling');
    throw new Error('configuration error');
  }
  
  const API_KEY = process.env.KIE_API_KEY;
  console.log('Checking environment variables in polling:');
  console.log('-exists:', !!process.env.KIE_API_KEY);
  console.log('-value (first 4 chars):', API_KEY ? `${API_KEY.substring(0, 4)}...` : 'not set');
  console.log('Starting to poll task status, task ID:', taskId);
  
  // Check task ID format, ensure it has correct prefix
  if (!taskId.includes('fluxkontext_') && !taskId.startsWith('flux')) {
    console.log('Task ID may have incorrect format, adding prefix');
    taskId = `fluxkontext_${taskId}`;
  }
  
  const statusUrl = `https://kieai.erweima.ai/api/v1/flux/kontext/status/${taskId}`;
  //console.log('Polling URL:', statusUrl);
  
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    console.log(`Polling attempt ${attempt + 1}/${maxAttempts}`);
    // Wait specified time
    await new Promise(resolve => setTimeout(resolve, pollInterval));
    
    try {
      // Check task status
      console.log(`Sending status request to: ${statusUrl}`);
      const response = await fetch(statusUrl, {
        headers: {
          'Authorization': `Bearer ${API_KEY}`
        }
      });
      
      console.log(`Status response code: ${response.status}`);
      
      // Try to read response text
      const responseText = await response.text();
      console.log(`Status response text: ${responseText}`);
      
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (jsonError) {
        console.error('Failed to parse status response JSON:', jsonError);
        continue; // Continue to next attempt
      }
      
      console.log(`Status response data: ${JSON.stringify(data)}`);
      
      if (!response.ok) {
        console.error(`Status request failed: ${response.status}`, data);
        // If 404, may be task ID format issue, try different format
        if (response.status === 404 && attempt === 0) {
          console.log('Trying to modify task ID format and retry');
          // If ID has prefix, try removing it
          if (taskId.includes('fluxkontext_')) {
            taskId = taskId.replace('fluxkontext_', '');
          } else {
            // Otherwise add prefix
            taskId = `fluxkontext_${taskId}`;
          }
          console.log(`Modified task ID: ${taskId}`);
          // Update URL
          const newStatusUrl = `https://kieai.erweima.ai/api/v1/flux/kontext/status/${taskId}`;
          console.log(`New polling URL: ${newStatusUrl}`);
          continue;
        }
        throw new Error(`Failed to get task status: ${data.message || 'Unknown error'}`);
      }
      
      const status = data.status;
      console.log(`Current task status: ${status}`);
      
      // If task completed or failed, return result
      if (status === 'COMPLETED' || status === 'SUCCESS') {
        console.log('Task completed, returning result');
        let imageUrl = '';
        if (data.result && data.result.images && data.result.images.length > 0) {
          imageUrl = data.result.images[0];
        } else if (data.images && data.images.length > 0) {
          imageUrl = data.images[0];
        } else if (data.resultUrls && data.resultUrls.length > 0) {
          imageUrl = data.resultUrls[0];
        }
        
        console.log(`Extracted image URL: ${imageUrl}`);
        
        if (!imageUrl) {
          console.error('Unable to extract image URL from completed task:', data);
          throw new Error('Task completed but no image URL returned');
        }
        
        return {
          imageUrl,
          taskId,
          status: 'COMPLETED'
        };
      } else if (status === 'FAILED' || status === 'FAILURE' || status === 'ERROR') {
        console.error('Task processing failed:', data);
        throw new Error(`Task processing failed: ${data.error || data.message || 'Unknown error'}`);
      }
      
      // If task still processing, continue polling
      console.log(`Task still processing, waiting for next poll...`);
    } catch (pollError) {
      console.error(`Error during polling (attempt ${attempt + 1}/${maxAttempts}):`, pollError);
      if (attempt === maxAttempts - 1) {
        throw pollError;
      }
      // Otherwise continue trying
    }
  }
  
  // If exceeded maximum polling attempts, return in-progress status instead of throwing error
  console.log('Polling attempts reached limit, returning in-progress status');
  return {
    taskId,
    status: 'PENDING',
    message: 'Task still processing, please check results later'
  };
} 