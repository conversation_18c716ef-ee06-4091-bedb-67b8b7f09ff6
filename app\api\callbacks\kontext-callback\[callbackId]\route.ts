import { NextResponse } from "next/server";
import { getSupabaseClient } from "@/models/db";
import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { v4 as uuidv4 } from 'uuid';

export const runtime = 'edge';

// Initialize S3 client for Cloudflare R2
const s3Client = new S3Client({
  region: "auto",
  endpoint: process.env.STORAGE_ENDPOINT,
  credentials: {
    accessKeyId: process.env.STORAGE_ACCESS_KEY || "",
    secretAccessKey: process.env.STORAGE_SECRET_KEY || "",
  },
  forcePathStyle: true
});

export async function POST(req: Request, { params }: { params: { callbackId: string } }) {
  try {
    const callbackId = params.callbackId;

    if (!callbackId) {
      return NextResponse.json(
        { error: "Callback ID is required" },
        { status: 400 }
      );
    }

    const data = await req.json();
    console.log(`Received Kontext callback, ID ${callbackId}:`, data);

    // Check callback data - handle different response formats
    let imageUrl;
    if (data.response && data.response.resultImageUrl) {
      // Handle the format from your API response
      imageUrl = data.response.resultImageUrl;
    } else if (data.imageUrl) {
      // Handle direct imageUrl format
      imageUrl = data.imageUrl;
    } else if (data.resultImageUrl) {
      // Handle direct resultImageUrl format
      imageUrl = data.resultImageUrl;
    }

    if (!imageUrl) {
      console.error("Invalid callback data - missing image URL:", JSON.stringify(data, null, 2));
      return NextResponse.json(
        { error: "Invalid callback data - missing image URL" },
        { status: 400 }
      );
    }

    console.log(`Processing image URL: ${imageUrl}`);

    // Upload image to R2 first
    let r2ImageUrl = imageUrl; // fallback to original URL
    try {
      // Generate unique filename for R2
      const uniqueFilename = `restore-old-photos-${uuidv4()}.jpg`;
      console.log(`Uploading image to R2 with filename: ${uniqueFilename}`);

      // Download the image from the AI service
      const imageResponse = await fetch(imageUrl);
      if (!imageResponse.ok) {
        throw new Error(`Failed to download image: ${imageResponse.status}`);
      }

      const imageBuffer = await imageResponse.arrayBuffer();
      console.log(`Downloaded image, size: ${imageBuffer.byteLength} bytes`);

      // Upload to R2
      const uploadCommand = new PutObjectCommand({
        Bucket: process.env.STORAGE_BUCKET || "",
        Key: uniqueFilename,
        Body: Buffer.from(imageBuffer),
        ContentType: 'image/jpeg',
      });

      await s3Client.send(uploadCommand);

      // Construct R2 URL
      r2ImageUrl = `${process.env.STORAGE_DOMAIN}/${uniqueFilename}`;
      console.log(`Successfully uploaded to R2: ${r2ImageUrl}`);

    } catch (uploadError) {
      console.error("Failed to upload to R2:", uploadError);
      // Continue with original URL if R2 upload fails
      console.log("Continuing with original image URL due to R2 upload failure");
    }

    // Update task status in database - use correct table name
    const supabase = getSupabaseClient();
    const { error } = await supabase
      .from("4o_generations")  // Fixed: use correct table name
      .update({
        status: "completed",
        completed_at: new Date().toISOString(),
        generated_image_url: r2ImageUrl  // Use R2 URL if available, otherwise original URL
      })
      .eq("callback_id", callbackId);

    if (error) {
      console.error("Error updating generation record:", error);
      return NextResponse.json(
        { error: "Failed to update generation record" },
        { status: 500 }
      );
    }

    console.log(`Successfully updated database record for callback ID: ${callbackId}`);

    return NextResponse.json({
      success: true,
      message: "Callback processed successfully",
      imageUrl: r2ImageUrl
    });
  } catch (error) {
    console.error("Error processing Kontext callback:", error);
    return NextResponse.json(
      { error: "Failed to process callback" },
      { status: 500 }
    );
  }
}